package com.jarvis.appium.examples;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.util.concurrent.TimeUnit;

/**
 * Complete APK testing example using MCP server
 * Demonstrates the full workflow: platform selection → session creation → app testing
 */
public class ApkTestingExample {
    
    private static final Logger logger = LoggerFactory.getLogger(ApkTestingExample.class);
    
    // Configuration - Update these paths for your setup
    private static final String APK_PATH = "C:\\path\\to\\your\\app.apk";  // Update this!
    private static final String PACKAGE_NAME = "com.example.yourapp";      // Update this!
    private static final String ACTIVITY_NAME = ".MainActivity";           // Update this!
    
    private static BufferedWriter writer;
    private static int requestId = 1;
    
    public static void main(String[] args) {
        try {
            testApkWithMcp();
        } catch (Exception e) {
            logger.error("APK testing failed", e);
        }
    }
    
    private static void testApkWithMcp() throws IOException, InterruptedException {
        logger.info("=== Starting APK Testing with MCP Server ===");
        
        String[] command = {"node", "C:\\POC\\AI\\MCP Integration Server\\jarvis-appium\\dist\\index.js"};
        
        ProcessBuilder processBuilder = new ProcessBuilder(command);
        processBuilder.redirectErrorStream(false);
        
        Process process = processBuilder.start();
        
        writer = new BufferedWriter(new OutputStreamWriter(process.getOutputStream()));
        BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
        BufferedReader errorReader = new BufferedReader(new InputStreamReader(process.getErrorStream()));
        
        // Start threads to read output
        startOutputReaders(reader, errorReader);
        
        // Wait for server to start
        Thread.sleep(3000);
        
        // Step 1: Initialize MCP connection
        logger.info("🔗 Step 1: Initializing MCP connection...");
        sendMcpRequest("initialize", "{\"protocolVersion\":\"2024-11-05\",\"clientInfo\":{\"name\":\"jarvis-appium-java-client\",\"version\":\"1.0.0\"},\"capabilities\":{\"roots\":{\"listChanged\":true},\"tools\":{\"listChanged\":true}}}");
        Thread.sleep(2000);
        
        // Step 2: Select Android platform
        logger.info("📱 Step 2: Selecting Android platform...");
        sendMcpRequest("tools/call", "{\"name\":\"select_platform\",\"arguments\":{\"platform\":\"android\"}}");
        Thread.sleep(2000);
        
        // Step 3: Create Android session with APK
        logger.info("🚀 Step 3: Creating Android session with APK...");
        String sessionCapabilities = String.format(
            "{\"platform\":\"android\",\"capabilities\":{" +
            "\"platformName\":\"Android\"," +
            "\"app\":\"" + APK_PATH.replace("\\", "\\\\") + "\"," +
            "\"appPackage\":\"" + PACKAGE_NAME + "\"," +
            "\"appActivity\":\"" + ACTIVITY_NAME + "\"," +
            "\"automationName\":\"UiAutomator2\"," +
            "\"newCommandTimeout\":300" +
            "}}"
        );
        sendMcpRequest("tools/call", "{\"name\":\"create_session\",\"arguments\":" + sessionCapabilities + "}");
        Thread.sleep(5000); // Wait longer for app to launch
        
        // Step 4: Take initial screenshot
        logger.info("📸 Step 4: Taking initial screenshot...");
        sendMcpRequest("tools/call", "{\"name\":\"appium_screenshot\",\"arguments\":{}}");
        Thread.sleep(2000);
        
        // Step 5: Generate locators for current screen
        logger.info("🔍 Step 5: Generating locators for current screen...");
        sendMcpRequest("tools/call", "{\"name\":\"generate_locators\",\"arguments\":{}}");
        Thread.sleep(3000);
        
        // Step 6: Example interactions (customize based on your app)
        logger.info("👆 Step 6: Performing example interactions...");
        
        // Find and interact with elements (examples - customize for your app)
        performExampleInteractions();
        
        // Step 7: Generate test code based on interactions
        logger.info("🧪 Step 7: Generating test code...");
        String testSteps = "[" +
            "\"Launch the application\"," +
            "\"Take screenshot of home screen\"," +
            "\"Find login button by ID\"," +
            "\"Click login button\"," +
            "\"Enter username in text field\"," +
            "\"Enter password in text field\"," +
            "\"Click submit button\"," +
            "\"Verify successful login\"" +
            "]";
        sendMcpRequest("tools/call", "{\"name\":\"appium_generate_tests\",\"arguments\":{\"steps\":" + testSteps + "}}");
        Thread.sleep(3000);
        
        // Clean up
        logger.info("🧹 Cleaning up...");
        writer.close();
        reader.close();
        errorReader.close();
        
        process.destroy();
        if (!process.waitFor(5, TimeUnit.SECONDS)) {
            process.destroyForcibly();
        }
        
        logger.info("=== APK Testing Complete ===");
    }
    
    private static void performExampleInteractions() throws IOException, InterruptedException {
        // Example 1: Find element by ID (customize for your app)
        logger.info("  🔍 Finding element by ID...");
        sendMcpRequest("tools/call", "{\"name\":\"appium_find_element\",\"arguments\":{\"strategy\":\"id\",\"selector\":\"com.example.yourapp:id/login_button\"}}");
        Thread.sleep(2000);
        
        // Example 2: Find element by text (customize for your app)
        logger.info("  🔍 Finding element by text...");
        sendMcpRequest("tools/call", "{\"name\":\"appium_find_element\",\"arguments\":{\"strategy\":\"xpath\",\"selector\":\"//*[@text='Login']\"}}");
        Thread.sleep(2000);
        
        // Example 3: Find element by accessibility ID
        logger.info("  🔍 Finding element by accessibility ID...");
        sendMcpRequest("tools/call", "{\"name\":\"appium_find_element\",\"arguments\":{\"strategy\":\"accessibility id\",\"selector\":\"login-button\"}}");
        Thread.sleep(2000);
        
        // Note: In real testing, you would:
        // 1. Use the returned element UUID from find_element
        // 2. Call appium_click with that UUID
        // 3. Call appium_set_value to enter text
        // 4. Call appium_get_text to verify content
        
        // Example of how to use returned element UUID:
        // String elementUUID = "uuid-returned-from-find-element";
        // sendMcpRequest("tools/call", "{\"name\":\"appium_click\",\"arguments\":{\"elementUUID\":\"" + elementUUID + "\"}}");
    }
    
    private static void sendMcpRequest(String method, String params) throws IOException {
        String request = "{\"jsonrpc\":\"2.0\",\"id\":\"" + (requestId++) + "\",\"method\":\"" + method + "\",\"params\":" + params + "}";
        logger.debug("Sending: {}", request);
        writer.write(request);
        writer.newLine();
        writer.flush();
    }
    
    private static void startOutputReaders(BufferedReader reader, BufferedReader errorReader) {
        // Thread to read stderr
        Thread errorThread = new Thread(() -> {
            try {
                String line;
                while ((line = errorReader.readLine()) != null) {
                    logger.debug("STDERR: {}", line);
                }
            } catch (IOException e) {
                logger.debug("Error reading stderr: {}", e.getMessage());
            }
        });
        errorThread.setDaemon(true);
        errorThread.start();
        
        // Thread to read stdout
        Thread readerThread = new Thread(() -> {
            try {
                String line;
                while ((line = reader.readLine()) != null) {
                    if (line.trim().startsWith("{")) {
                        logger.info("📨 MCP Response: {}", line);
                    } else {
                        logger.debug("Log: {}", line);
                    }
                }
            } catch (IOException e) {
                logger.debug("Error reading stdout: {}", e.getMessage());
            }
        });
        readerThread.setDaemon(true);
        readerThread.start();
    }
}
