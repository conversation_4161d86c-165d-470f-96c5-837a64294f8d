package com.jarvis.appium.examples;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.util.concurrent.TimeUnit;

/**
 * Cloud APK testing example using LambdaTest via MCP server
 * Demonstrates testing APK on real devices in the cloud
 */
public class CloudApkTestingExample {
    
    private static final Logger logger = LoggerFactory.getLogger(CloudApkTestingExample.class);
    
    // Configuration - Update these for your setup
    private static final String APK_PATH = "C:\\path\\to\\your\\app.apk";  // Local APK path
    private static final String DEVICE_NAME = "Galaxy S21";                // Target device
    private static final String PLATFORM_VERSION = "11";                  // Android version
    private static final String BUILD_NAME = "My App Test Build";
    private static final String TEST_NAME = "APK Functionality Test";
    
    private static BufferedWriter writer;
    private static int requestId = 1;
    
    public static void main(String[] args) {
        try {
            testApkOnCloud();
        } catch (Exception e) {
            logger.error("Cloud APK testing failed", e);
        }
    }
    
    private static void testApkOnCloud() throws IOException, InterruptedException {
        logger.info("=== Starting Cloud APK Testing with LambdaTest ===");
        
        String[] command = {"node", "C:\\POC\\AI\\MCP Integration Server\\jarvis-appium\\dist\\index.js"};
        
        ProcessBuilder processBuilder = new ProcessBuilder(command);
        processBuilder.redirectErrorStream(false);
        
        Process process = processBuilder.start();
        
        writer = new BufferedWriter(new OutputStreamWriter(process.getOutputStream()));
        BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
        BufferedReader errorReader = new BufferedReader(new InputStreamReader(process.getErrorStream()));
        
        // Start threads to read output
        startOutputReaders(reader, errorReader);
        
        // Wait for server to start
        Thread.sleep(3000);
        
        // Step 1: Initialize MCP connection
        logger.info("🔗 Step 1: Initializing MCP connection...");
        sendMcpRequest("initialize", "{\"protocolVersion\":\"2024-11-05\",\"clientInfo\":{\"name\":\"jarvis-appium-java-client\",\"version\":\"1.0.0\"},\"capabilities\":{\"roots\":{\"listChanged\":true},\"tools\":{\"listChanged\":true}}}");
        Thread.sleep(2000);
        
        // Step 2: Select Android platform
        logger.info("📱 Step 2: Selecting Android platform...");
        sendMcpRequest("tools/call", "{\"name\":\"select_platform\",\"arguments\":{\"platform\":\"android\"}}");
        Thread.sleep(2000);
        
        // Step 3: Upload APK to LambdaTest
        logger.info("☁️ Step 3: Uploading APK to LambdaTest cloud...");
        String uploadParams = "{\"appPath\":\"" + APK_PATH.replace("\\", "\\\\") + "\",\"appName\":\"MyTestApp\"}";
        sendMcpRequest("tools/call", "{\"name\":\"upload_app_lambdatest\",\"arguments\":" + uploadParams + "}");
        Thread.sleep(10000); // Upload can take time
        
        // Step 4: Create LambdaTest session
        logger.info("🚀 Step 4: Creating LambdaTest session...");
        String sessionParams = createLambdaTestSessionParams();
        sendMcpRequest("tools/call", "{\"name\":\"create_lambdatest_session\",\"arguments\":" + sessionParams + "}");
        Thread.sleep(8000); // Cloud session takes longer to start
        
        // Step 5: Take initial screenshot
        logger.info("📸 Step 5: Taking initial screenshot...");
        sendMcpRequest("tools/call", "{\"name\":\"appium_screenshot\",\"arguments\":{}}");
        Thread.sleep(3000);
        
        // Step 6: Generate locators
        logger.info("🔍 Step 6: Generating locators...");
        sendMcpRequest("tools/call", "{\"name\":\"generate_locators\",\"arguments\":{}}");
        Thread.sleep(3000);
        
        // Step 7: Perform comprehensive testing
        logger.info("🧪 Step 7: Performing comprehensive app testing...");
        performComprehensiveTesting();
        
        // Step 8: Query Appium documentation for best practices
        logger.info("📚 Step 8: Querying Appium documentation...");
        sendMcpRequest("tools/call", "{\"name\":\"appium_documentation_query\",\"arguments\":{\"query\":\"How to handle mobile app permissions and alerts in Android testing\"}}");
        Thread.sleep(3000);
        
        // Step 9: Generate comprehensive test suite
        logger.info("🧪 Step 9: Generating comprehensive test suite...");
        String comprehensiveTestSteps = createComprehensiveTestSteps();
        sendMcpRequest("tools/call", "{\"name\":\"appium_generate_tests\",\"arguments\":{\"steps\":" + comprehensiveTestSteps + "}}");
        Thread.sleep(3000);
        
        // Clean up
        logger.info("🧹 Cleaning up...");
        writer.close();
        reader.close();
        errorReader.close();
        
        process.destroy();
        if (!process.waitFor(5, TimeUnit.SECONDS)) {
            process.destroyForcibly();
        }
        
        logger.info("=== Cloud APK Testing Complete ===");
    }
    
    private static String createLambdaTestSessionParams() {
        return "{" +
            "\"platform\":\"android\"," +
            "\"deviceName\":\"" + DEVICE_NAME + "\"," +
            "\"platformVersion\":\"" + PLATFORM_VERSION + "\"," +
            "\"app\":\"lt://APP_ID_FROM_UPLOAD\"," + // This would be returned from upload step
            "\"buildName\":\"" + BUILD_NAME + "\"," +
            "\"testName\":\"" + TEST_NAME + "\"," +
            "\"ltOptions\":{" +
                "\"devicelog\":true," +
                "\"visual\":true," +
                "\"video\":true," +
                "\"autoAcceptAlerts\":true," +
                "\"autoGrantPermissions\":true," +
                "\"timezone\":\"UTC\"," +
                "\"location\":\"US\"" +
            "}" +
        "}";
    }
    
    private static void performComprehensiveTesting() throws IOException, InterruptedException {
        // Test 1: App launch and initial state
        logger.info("  🔍 Test 1: Verifying app launch...");
        sendMcpRequest("tools/call", "{\"name\":\"appium_find_element\",\"arguments\":{\"strategy\":\"xpath\",\"selector\":\"//*[@resource-id='android:id/content']\"}}");
        Thread.sleep(2000);
        
        // Test 2: Navigation elements
        logger.info("  🔍 Test 2: Finding navigation elements...");
        sendMcpRequest("tools/call", "{\"name\":\"appium_find_element\",\"arguments\":{\"strategy\":\"xpath\",\"selector\":\"//*[@content-desc='Navigate up']\"}}");
        Thread.sleep(2000);
        
        // Test 3: Input fields
        logger.info("  🔍 Test 3: Finding input fields...");
        sendMcpRequest("tools/call", "{\"name\":\"appium_find_element\",\"arguments\":{\"strategy\":\"class name\",\"selector\":\"android.widget.EditText\"}}");
        Thread.sleep(2000);
        
        // Test 4: Buttons
        logger.info("  🔍 Test 4: Finding buttons...");
        sendMcpRequest("tools/call", "{\"name\":\"appium_find_element\",\"arguments\":{\"strategy\":\"class name\",\"selector\":\"android.widget.Button\"}}");
        Thread.sleep(2000);
        
        // Test 5: Lists and scrollable content
        logger.info("  🔍 Test 5: Finding scrollable content...");
        sendMcpRequest("tools/call", "{\"name\":\"appium_find_element\",\"arguments\":{\"strategy\":\"class name\",\"selector\":\"android.widget.ListView\"}}");
        Thread.sleep(2000);
        
        // Take screenshot after each major test
        logger.info("  📸 Taking progress screenshot...");
        sendMcpRequest("tools/call", "{\"name\":\"appium_screenshot\",\"arguments\":{}}");
        Thread.sleep(2000);
    }
    
    private static String createComprehensiveTestSteps() {
        return "[" +
            "\"Launch application and verify successful startup\"," +
            "\"Take screenshot of main screen\"," +
            "\"Verify app permissions are granted\"," +
            "\"Test navigation drawer or menu functionality\"," +
            "\"Find and interact with primary action buttons\"," +
            "\"Test text input fields with valid data\"," +
            "\"Test text input fields with invalid data\"," +
            "\"Verify form validation messages\"," +
            "\"Test scroll functionality in lists\"," +
            "\"Test pull-to-refresh if available\"," +
            "\"Test device rotation (portrait/landscape)\"," +
            "\"Test app backgrounding and foregrounding\"," +
            "\"Verify data persistence after app restart\"," +
            "\"Test network connectivity scenarios\"," +
            "\"Verify error handling for network failures\"," +
            "\"Test accessibility features\"," +
            "\"Take final screenshot and verify app state\"" +
        "]";
    }
    
    private static void sendMcpRequest(String method, String params) throws IOException {
        String request = "{\"jsonrpc\":\"2.0\",\"id\":\"" + (requestId++) + "\",\"method\":\"" + method + "\",\"params\":" + params + "}";
        logger.debug("Sending: {}", request);
        writer.write(request);
        writer.newLine();
        writer.flush();
    }
    
    private static void startOutputReaders(BufferedReader reader, BufferedReader errorReader) {
        // Thread to read stderr
        Thread errorThread = new Thread(() -> {
            try {
                String line;
                while ((line = errorReader.readLine()) != null) {
                    logger.debug("STDERR: {}", line);
                }
            } catch (IOException e) {
                logger.debug("Error reading stderr: {}", e.getMessage());
            }
        });
        errorThread.setDaemon(true);
        errorThread.start();
        
        // Thread to read stdout
        Thread readerThread = new Thread(() -> {
            try {
                String line;
                while ((line = reader.readLine()) != null) {
                    if (line.trim().startsWith("{")) {
                        logger.info("📨 MCP Response: {}", line);
                    } else {
                        logger.debug("Log: {}", line);
                    }
                }
            } catch (IOException e) {
                logger.debug("Error reading stdout: {}", e.getMessage());
            }
        });
        readerThread.setDaemon(true);
        readerThread.start();
    }
}
