package com.jarvis.appium.examples;

import java.io.*;
import java.util.concurrent.TimeUnit;

/**
 * Test the create_session tool specifically
 */
public class CreateSessionTest {
    
    private static BufferedWriter writer;
    private static BufferedReader reader;
    private static BufferedReader errorReader;
    private static int requestId = 1;
    
    public static void main(String[] args) {
        try {
            testCreateSession();
        } catch (Exception e) {
            System.err.println("Create session test failed: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static void testCreateSession() throws IOException, InterruptedException {
        System.out.println("=== Create Session Test ===");
        
        // Start MCP server
        String[] command = {"node", "dist/index.js"};
        ProcessBuilder processBuilder = new ProcessBuilder(command);
        processBuilder.directory(new File("C:\\POC\\AI\\MCP Integration Server\\jarvis-appium"));
        processBuilder.redirectErrorStream(false);
        
        System.out.println("Starting MCP server...");
        Process process = processBuilder.start();
        
        writer = new BufferedWriter(new OutputStreamWriter(process.getOutputStream()));
        reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
        errorReader = new BufferedReader(new InputStreamReader(process.getErrorStream()));
        
        // Start output readers in separate threads
        Thread outputThread = new Thread(() -> {
            try {
                String line;
                while ((line = reader.readLine()) != null) {
                    System.out.println("SERVER OUTPUT: " + line);
                }
            } catch (IOException e) {
                System.err.println("Error reading server output: " + e.getMessage());
            }
        });
        
        Thread errorThread = new Thread(() -> {
            try {
                String line;
                while ((line = errorReader.readLine()) != null) {
                    System.err.println("SERVER ERROR: " + line);
                }
            } catch (IOException e) {
                System.err.println("Error reading server error: " + e.getMessage());
            }
        });
        
        outputThread.start();
        errorThread.start();
        
        // Wait for server to start
        System.out.println("Waiting for server to start...");
        Thread.sleep(5000);
        
        // Initialize MCP
        System.out.println("Initializing MCP connection...");
        sendMcpRequest("initialize", "{\"protocolVersion\":\"2024-11-05\",\"clientInfo\":{\"name\":\"session-test\",\"version\":\"1.0.0\"},\"capabilities\":{}}");
        Thread.sleep(2000);
        
        // Select platform
        System.out.println("Selecting Android platform...");
        sendMcpRequest("tools/call", "{\"name\":\"select_platform\",\"arguments\":{\"platform\":\"android\"}}");
        Thread.sleep(2000);
        
        // Test create_session with SauceLabs app
        System.out.println("Creating session with SauceLabs app...");
        String createSessionParams = "{"
            + "\"name\":\"create_session\","
            + "\"arguments\":{"
                + "\"platform\":\"android\","
                + "\"capabilities\":{"
                    + "\"platformName\":\"Android\","
                    + "\"automationName\":\"UiAutomator2\","
                    + "\"deviceName\":\"emulator-5554\","
                    + "\"app\":\"C:\\\\POC\\\\AI\\\\MCP Integration Server\\\\mcp-client-integration\\\\Android.SauceLabs.Mobile.Sample.app.2.7.1.apk\","
                    + "\"appPackage\":\"com.saucelabs.mydemoapp.android\","
                    + "\"appActivity\":\".view.activities.SplashActivity\","
                    + "\"noReset\":false,"
                    + "\"fullReset\":true"
                + "}"
            + "}"
        + "}";
        
        sendMcpRequest("tools/call", createSessionParams);
        
        // Wait longer for session creation
        System.out.println("Waiting for session creation...");
        Thread.sleep(15000);
        
        // Check if process is still alive
        if (process.isAlive()) {
            System.out.println("Server is still running after session creation");
        } else {
            System.err.println("Server terminated during session creation! Exit code: " + process.exitValue());
        }
        
        // Cleanup
        System.out.println("Cleaning up...");
        try {
            writer.close();
            reader.close();
            errorReader.close();
            process.destroyForcibly();
        } catch (Exception e) {
            System.err.println("Error during cleanup: " + e.getMessage());
        }
        
        System.out.println("Test completed");
    }
    
    private static void sendMcpRequest(String method, String params) throws IOException {
        String request = "{\"jsonrpc\":\"2.0\",\"id\":\"" + (requestId++) + "\",\"method\":\"" + method + "\",\"params\":" + params + "}";
        System.out.println("SENDING: " + request);
        writer.write(request);
        writer.newLine();
        writer.flush();
    }
}
